<?php

use App\Http\Controllers\AdminRecommendationController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserAgreementController;
use App\Http\Controllers\WelcomeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 主要 Web 路由
|--------------------------------------------------------------------------
|
| 這裡定義應用程式的主要 Web 路由，包括首頁、儀表板和使用者協議等核心功能
|
*/

// 公開路由 (無需認證)
/** 首頁 - 系統歡迎頁面 */
Route::get('/', [WelcomeController::class, 'index'])
    ->name('home');

// 認證後路由 (需要登入但不需要同意使用者協議)
Route::middleware(['auth', 'verified'])->group(function () {
    /** 使用者協議頁面 - 顯示使用者協議 */
    Route::get('/user-agreement', [UserAgreementController::class, 'show'])
        ->name('user-agreement.show');

    /** 使用者協議確認 - 處理使用者同意協議 */
    Route::post('/user-agreement', [UserAgreementController::class, 'store'])
        ->name('user-agreement.store');
});

// 受保護路由 (需要登入且同意使用者協議)
Route::middleware(['auth', 'verified', 'check.user.agreement', 'check.system.access'])->group(function () {
    /** 主儀表板 - 根據使用者角色顯示對應的儀表板 */
    Route::get('/dashboard', [DashboardController::class, 'index'])
        ->name('dashboard');
});

// 管理員專用路由
Route::middleware(['auth', 'verified', 'role:admin', 'check.system.access'])->prefix('admin')->name('admin.')->group(function () {
    /** 管理員推薦函管理 - 查看所有推薦函 */
    Route::get('/recommendations', [AdminRecommendationController::class, 'index'])
        ->name('recommendations.index');

    /** 系統設定管理 */
    Route::get('/system-settings', [\App\Http\Controllers\Admin\SystemSettingController::class, 'index'])
        ->name('system-settings.index');
    Route::put('/system-settings', [\App\Http\Controllers\Admin\SystemSettingController::class, 'update'])
        ->name('system-settings.update');
    Route::put('/system-settings/{key}', [\App\Http\Controllers\Admin\SystemSettingController::class, 'updateSingle'])
        ->name('system-settings.update-single');
    Route::post('/system-settings/reset-defaults', [\App\Http\Controllers\Admin\SystemSettingController::class, 'resetDefaults'])
        ->name('system-settings.reset-defaults');
    Route::post('/system-settings/toggle-questionnaire-lock', [\App\Http\Controllers\Admin\SystemSettingController::class, 'toggleQuestionnaireLock'])
        ->name('system-settings.toggle-questionnaire-lock');
    Route::post('/system-settings/toggle-maintenance-mode', [\App\Http\Controllers\Admin\SystemSettingController::class, 'toggleMaintenanceMode'])
        ->name('system-settings.toggle-maintenance-mode');
    Route::post('/system-settings/sync-external-data', [\App\Http\Controllers\Admin\SystemSettingController::class, 'syncExternalData'])
        ->name('system-settings.sync-external-data');
    Route::get('/system-settings/check-api-connection', [\App\Http\Controllers\Admin\SystemSettingController::class, 'checkApiConnection'])
        ->name('system-settings.check-api-connection');
    Route::get('/system-settings/status', [\App\Http\Controllers\Admin\SystemSettingController::class, 'getSystemStatus'])
        ->name('system-settings.status');

    /** PDF管理 */
    Route::get('/pdf-management', [\App\Http\Controllers\Admin\PdfManagementController::class, 'index'])
        ->name('pdf-management.index');
    Route::post('/pdf-management/upload', [\App\Http\Controllers\Admin\PdfManagementController::class, 'uploadTemplate'])
        ->name('pdf-management.upload');
    Route::delete('/pdf-management/delete/{id}', [\App\Http\Controllers\Admin\PdfManagementController::class, 'deleteTemplate'])
        ->name('pdf-management.delete');
    Route::post('/pdf-management/toggle/{id}', [\App\Http\Controllers\Admin\PdfManagementController::class, 'toggleTemplate'])
        ->name('pdf-management.toggle');
    Route::get('/pdf-management/download/{id}', [\App\Http\Controllers\Admin\PdfManagementController::class, 'downloadTemplate'])
        ->name('pdf-management.download');
    Route::get('/pdf-management/settings', [\App\Http\Controllers\Admin\PdfManagementController::class, 'getSettings'])
        ->name('pdf-management.settings');

    /** 系統日誌管理 */
    Route::get('/system-logs', [\App\Http\Controllers\Admin\SystemLogController::class, 'index'])
        ->name('system-logs.index');
    Route::get('/system-logs/{id}', [\App\Http\Controllers\Admin\SystemLogController::class, 'show'])
        ->name('system-logs.show');
    Route::post('/system-logs/cleanup', [\App\Http\Controllers\Admin\SystemLogController::class, 'cleanup'])
        ->name('system-logs.cleanup');
    Route::get('/system-logs/export', [\App\Http\Controllers\Admin\SystemLogController::class, 'export'])
        ->name('system-logs.export');

    /** 問卷模板管理 */
    Route::get('/questionnaire-templates', [\App\Http\Controllers\QuestionnaireController::class, 'index'])
        ->name('questionnaire-templates.index');

    /** Email 日誌管理 */
    Route::get('/email-logs', [\App\Http\Controllers\Admin\EmailLogController::class, 'index'])
        ->name('email-logs.index');
    Route::get('/email-logs/{id}', [\App\Http\Controllers\Admin\EmailLogController::class, 'show'])
        ->name('email-logs.show');
    Route::get('/email-logs/export', [\App\Http\Controllers\Admin\EmailLogController::class, 'export'])
        ->name('email-logs.export');
    Route::post('/email-logs/cleanup', [\App\Http\Controllers\Admin\EmailLogController::class, 'cleanup'])
        ->name('email-logs.cleanup');

    /** 郵件設定管理 */
    Route::get('/mail-settings', [\App\Http\Controllers\Admin\MailSettingsController::class, 'index'])
        ->name('mail-settings.index');
    Route::put('/mail-settings', [\App\Http\Controllers\Admin\MailSettingsController::class, 'update'])
        ->name('mail-settings.update');
    Route::post('/mail-settings/test', [\App\Http\Controllers\Admin\MailSettingsController::class, 'testApi'])
        ->name('mail-settings.test');
    Route::get('/mail-settings/stats', [\App\Http\Controllers\Admin\MailSettingsController::class, 'getStats'])
        ->name('mail-settings.stats');

    /** 登入記錄管理 */
    Route::get('/login-logs', [\App\Http\Controllers\Admin\LoginLogController::class, 'index'])
        ->name('login-logs.index');
    Route::get('/login-logs/{id}', [\App\Http\Controllers\Admin\LoginLogController::class, 'show'])
        ->name('login-logs.show');
    Route::post('/login-logs/cleanup', [\App\Http\Controllers\Admin\LoginLogController::class, 'cleanup'])
        ->name('login-logs.cleanup');
    Route::get('/login-logs/export', [\App\Http\Controllers\Admin\LoginLogController::class, 'export'])
        ->name('login-logs.export');

    /** 測試路由 */
    Route::get('/test', function () {
        return response()->json(['message' => 'Admin routes working']);
    })->name('test');
});


/** 認證相關路由 (登入、登出) */
require __DIR__ . '/auth.php';

/** 推薦函功能相關路由 (申請人、推薦人、問卷等) */
require __DIR__ . '/recommendation.php';
