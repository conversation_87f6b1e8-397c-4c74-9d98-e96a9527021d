<?php

namespace App\Services;

use App\Models\SystemSetting;
use App\Models\SystemLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 外部API同步服務
 * 
 * 負責與外部系統進行資料同步，包括招生期間、考試資訊等
 */
class ExternalApiSyncService
{
    /**
     * API基礎設定
     */
    private const API_BASE_URL = 'http://localhost:18001/index.php/api/v1/recommendation_system';
    private const API_SECRET = 'OdmzICpznuM7O48V3gJCJaejNWwabcpG'; // 將移至環境變數
    private const API_TIMEOUT = 30; // 30秒超時
    
    /**
     * 同步招生期間資料
     * 
     * @return array 同步結果
     */
    public function syncExamPeriods(): array
    {
        try {
            $apiUrl = self::API_BASE_URL . '/sync_exam_period';
            
            Log::info('開始同步招生期間資料', ['api_url' => $apiUrl]);
            
            $response = Http::timeout(self::API_TIMEOUT)
                ->asForm()
                ->post($apiUrl, [
                    'nuu_api_key' => self::API_SECRET,
                    'nuu_api_id' => 'sync_exam_period',
                ]);

            if (!$response->successful()) {
                throw new \Exception('API 請求失敗，狀態碼：' . $response->status());
            }

            $data = $response->json();

            if (!isset($data['status']) || $data['status'] !== 'success') {
                throw new \Exception('API 回傳非 success：' . json_encode($data));
            }

            // 處理同步的資料
            $examPeriods = $data['data'] ?? [];
            $syncResult = $this->processExamPeriods($examPeriods);

            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                '成功同步招生期間資料',
                [
                    'synced_periods' => count($examPeriods),
                    'updated_settings' => $syncResult['updated_count']
                ]
            );

            return [
                'success' => true,
                'message' => '招生期間資料同步成功',
                'data' => $syncResult
            ];

        } catch (\Exception $e) {
            Log::error('同步招生期間資料失敗', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '同步招生期間資料失敗',
                $e
            );

            // 設定預設值
            $defaultResult = $this->setDefaultExamPeriods();

            return [
                'success' => false,
                'message' => '同步失敗，已設定預設值：' . $e->getMessage(),
                'data' => $defaultResult
            ];
        }
    }

    /**
     * 處理招生期間資料
     * 
     * @param array $examPeriods
     * @return array
     */
    private function processExamPeriods(array $examPeriods): array
    {
        $updatedCount = 0;
        $processedPeriods = [];

        foreach ($examPeriods as $period) {
            if (!isset($period['exam_id'], $period['exam_name'], $period['app_date1_start'], $period['app_date1_end'])) {
                Log::warning('招生期間資料格式不完整', ['period' => $period]);
                continue;
            }

            // 更新系統設定
            $startKey = "recruitment.period_start.{$period['exam_id']}";
            $endKey = "recruitment.period_end.{$period['exam_id']}";
            $nameKey = "recruitment.name.{$period['exam_id']}";

            SystemSetting::set($startKey, $period['app_date1_start'], SystemSetting::TYPE_DATETIME, SystemSetting::CATEGORY_TIMING, "招生開始時間 - {$period['exam_name']}");
            SystemSetting::set($endKey, $period['app_date1_end'], SystemSetting::TYPE_DATETIME, SystemSetting::CATEGORY_TIMING, "招生結束時間 - {$period['exam_name']}");
            SystemSetting::set($nameKey, $period['exam_name'], SystemSetting::TYPE_STRING, SystemSetting::CATEGORY_GENERAL, "招生名稱 - {$period['exam_name']}");

            $processedPeriods[] = [
                'exam_id' => $period['exam_id'],
                'exam_name' => $period['exam_name'],
                'start_time' => $period['app_date1_start'],
                'end_time' => $period['app_date1_end']
            ];

            $updatedCount++;
        }

        // 清除相關快取
        Cache::forget('system_settings_recruitment');

        return [
            'updated_count' => $updatedCount,
            'processed_periods' => $processedPeriods
        ];
    }

    /**
     * 設定預設招生期間
     * 
     * @return array
     */
    private function setDefaultExamPeriods(): array
    {
        $defaultPeriods = [
            [
                'exam_id' => 'default',
                'exam_name' => '預設招生期間',
                'app_date1_start' => now()->format('Y-m-d H:i:s'),
                'app_date1_end' => now()->addMonths(3)->format('Y-m-d H:i:s')
            ]
        ];

        Log::info('設定預設招生期間', ['periods' => $defaultPeriods]);

        return $this->processExamPeriods($defaultPeriods);
    }

    /**
     * 同步系統基本設定
     * 
     * @return array
     */
    public function syncSystemSettings(): array
    {
        try {
            // 這裡可以擴展其他系統設定的同步
            $results = [];
            
            // 同步招生期間
            $examResult = $this->syncExamPeriods();
            $results['exam_periods'] = $examResult;

            $overallSuccess = $examResult['success'];

            return [
                'success' => $overallSuccess,
                'message' => $overallSuccess ? '系統設定同步完成' : '部分設定同步失敗',
                'results' => $results
            ];

        } catch (\Exception $e) {
            Log::error('同步系統設定失敗', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '系統設定同步失敗：' . $e->getMessage(),
                'results' => []
            ];
        }
    }

    /**
     * 檢查API連線狀態
     * 
     * @return array
     */
    public function checkApiConnection(): array
    {
        try {
            $response = Http::timeout(10)
                ->get(self::API_BASE_URL . '/health');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'API連線正常',
                    'response_time' => $response->transferStats?->getTransferTime() ?? 0
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API連線異常，狀態碼：' . $response->status()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API連線失敗：' . $e->getMessage()
            ];
        }
    }
}
