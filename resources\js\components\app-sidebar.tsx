import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { useLanguage } from '@/hooks/use-language';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { LayoutGrid, BarChart3, FileText, Settings, Activity, Mail, ScanFace, File } from 'lucide-react';
import AppLogo from './app-logo';

export function AppSidebar() {
    const { t } = useLanguage();
    const { auth } = usePage<SharedData>().props;
    const isAdmin = auth?.user?.role === 'admin';

    // 根據使用者角色動態生成導航項目，支援分組和折疊
    const getMainNavItems = (): NavItem[] => {
        const baseItems: NavItem[] = [
            {
                title: t('common.dashboard'),
                href: '/dashboard',
                icon: LayoutGrid,
            },
        ];

        // 只有管理員才顯示管理功能
        if (isAdmin) {
            return [
                ...baseItems,

                {
                    title: '問卷模板',
                    href: '/admin/questionnaire-templates',
                    icon: FileText,
                },
                {
                    title: '推薦函狀態',
                    href: '/admin/recommendations',
                    icon: BarChart3,
                },

                {
                    title: 'PDF管理',
                    href: '/admin/pdf-management',
                    icon: File,
                },

                {
                    title: '系統日誌',
                    href: '/admin/system-logs',
                    icon: Activity,
                },

                {
                    title: '登入日誌',
                    href: '/admin/login-logs',
                    icon: ScanFace,
                },
                {
                    title: '發信日誌',
                    href: '/admin/email-logs',
                    icon: Mail,
                },
                {
                    title: '系統設定',
                    href: '/admin/system-settings',
                    icon: Settings,
                },
            ];
        }

        return baseItems;
    };

    const mainNavItems = getMainNavItems();

    // Admin-only navigation items
    const adminNavItems: NavItem[] = isAdmin ? [] : [];

    const footerNavItems: NavItem[] = [];

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={[...mainNavItems, ...adminNavItems]} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
