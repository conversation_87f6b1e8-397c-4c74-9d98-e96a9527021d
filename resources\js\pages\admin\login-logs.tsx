import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, LogIn, User, Shield, Users, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';

interface LoginLog {
    id: number;
    user_id: number;
    login_at: string;
    ip_address: string;
    user_agent: string;
    login_method: string;
    session_id?: string;
    created_at: string;
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface LoginLogsProps {
    loginLogs: {
        data: LoginLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function LoginLogs({ loginLogs }: LoginLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState('all');
    const [methodFilter, setMethodFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '登入記錄', href: '/admin/login-logs' },
    ];

    // 角色徽章
    const getRoleBadge = (role: string) => {
        switch (role) {
            case 'admin':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-red-100 text-red-800">
                        <Shield className="h-3 w-3" />
                        管理員
                    </Badge>
                );
            case 'applicant':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-blue-100 text-blue-800">
                        <User className="h-3 w-3" />
                        申請人
                    </Badge>
                );
            case 'recommender':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-800">
                        <Users className="h-3 w-3" />
                        推薦人
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{role}</Badge>;
        }
    };

    // 登入方式徽章
    const getMethodBadge = (method: string | undefined | null) => {
        if (!method) {
            return <Badge variant="outline">未知</Badge>;
        }
        switch (method.toLowerCase()) {
            case 'password':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        密碼登入
                    </Badge>
                );
            case 'token':
                return (
                    <Badge variant="outline" className="text-green-700">
                        Token 登入
                    </Badge>
                );
            case 'sso':
                return (
                    <Badge variant="outline" className="text-purple-700">
                        SSO 登入
                    </Badge>
                );
            default:
                return <Badge variant="outline">{method}</Badge>;
        }
    };

    // 過濾登入記錄
    const filteredLogs = loginLogs.data.filter((log) => {
        const matchesSearch =
            log.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.ip_address.includes(searchTerm);

        const matchesRole = roleFilter === 'all' || log.user.role === roleFilter;
        const matchesMethod = methodFilter === 'all' || log.login_method?.toLowerCase() === methodFilter;

        return matchesSearch && matchesRole && matchesMethod;
    });

    // 獲取所有登入方式
    const methods = Array.from(
        new Set(loginLogs.data.map((log) => log.login_method?.toLowerCase() || 'unknown').filter((method) => method !== 'unknown')),
    );

    // 獲取瀏覽器資訊
    const getBrowserInfo = (userAgent: string) => {
        // 簡單的瀏覽器檢測
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return '未知瀏覽器';
    };

    // 匯出登入記錄
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (roleFilter !== 'all') params.append('role', roleFilter);
        if (methodFilter !== 'all') params.append('method', methodFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/login-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊記錄
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 90 天前的舊登入記錄嗎？此操作無法復原。')) {
            router.post(
                '/admin/login-logs/cleanup',
                { days: 90 },
                {
                    onSuccess: () => {
                        alert('舊記錄已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="登入記錄" description="查看和管理使用者登入記錄和安全日誌">
            <Head title="登入記錄" />

            <div className="space-y-6 p-6">
                {/* todo 篩選和搜尋區域(元件化) */}

                {/* todo 統計資訊(元件化) */}

                {/* todo 登入記錄列表(元件化，使用table呈現) */}
            </div>
        </AdminLayout>
    );
}
