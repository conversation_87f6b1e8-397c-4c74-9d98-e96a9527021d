import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Activity, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { useState } from 'react';

interface SystemLog {
    id: number;
    user_id?: number;
    action: string;
    description: string;
    level: string;
    ip_address: string;
    user_agent: string;
    context?: any;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface SystemLogsProps {
    logs: {
        data: SystemLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function SystemLogs({ logs }: SystemLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [levelFilter, setLevelFilter] = useState('all');
    const [actionFilter, setActionFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統日誌', href: '/admin/system-logs' },
    ];

    // 日誌等級徽章
    const getLevelBadge = (level: string | undefined | null) => {
        if (!level) {
            return <Badge variant="secondary">未知</Badge>;
        }
        switch (level.toLowerCase()) {
            case 'error':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-red-100 text-red-800">
                        <AlertTriangle className="h-3 w-3" />
                        錯誤
                    </Badge>
                );
            case 'warning':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="h-3 w-3" />
                        警告
                    </Badge>
                );
            case 'info':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-blue-100 text-blue-800">
                        <Info className="h-3 w-3" />
                        資訊
                    </Badge>
                );
            case 'success':
                return (
                    <Badge variant="secondary" className="flex items-center gap-1 bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3" />
                        成功
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{level}</Badge>;
        }
    };

    // 操作類型徽章
    const getActionBadge = (action: string) => {
        const actionMap: { [key: string]: { label: string; color: string } } = {
            CREATE: { label: '新增', color: 'bg-green-100 text-green-800' },
            UPDATE: { label: '更新', color: 'bg-blue-100 text-blue-800' },
            DELETE: { label: '刪除', color: 'bg-red-100 text-red-800' },
            VIEW: { label: '查看', color: 'bg-gray-100 text-gray-800' },
            LOGIN: { label: '登入', color: 'bg-purple-100 text-purple-800' },
            LOGOUT: { label: '登出', color: 'bg-orange-100 text-orange-800' },
            EXPORT: { label: '匯出', color: 'bg-indigo-100 text-indigo-800' },
            IMPORT: { label: '匯入', color: 'bg-pink-100 text-pink-800' },
        };

        const actionInfo = actionMap[action.toUpperCase()] || { label: action, color: 'bg-gray-100 text-gray-800' };

        return (
            <Badge variant="secondary" className={actionInfo.color}>
                {actionInfo.label}
            </Badge>
        );
    };

    // 過濾日誌
    const filteredLogs = logs.data.filter((log) => {
        const matchesSearch =
            log.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            log.action?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesLevel = levelFilter === 'all' || log.level?.toLowerCase() === levelFilter;
        const matchesAction = actionFilter === 'all' || log.action?.toUpperCase() === actionFilter;

        return matchesSearch && matchesLevel && matchesAction;
    });

    // 獲取所有操作類型
    const actions = Array.from(new Set(logs.data.map((log) => log.action?.toUpperCase() || 'UNKNOWN').filter((action) => action !== 'UNKNOWN')));

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統日誌" description="查看和管理系統操作記錄和錯誤日誌">
            <Head title="系統日誌" />

            <div className="space-y-6 p-6">
                {/* todo 篩選和搜尋區域(元件化) */}

                {/* todo 統計資訊(元件化) */}

                {/* todo 日誌列表(元件化，使用table呈現) */}
            </div>
        </AdminLayout>
    );
}
