import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Activity, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { useState } from 'react';
import StatusBadge from '@/components/admin/StatusBadge';
import SearchAndFilter from '@/components/admin/SearchAndFilter';
import DataTable from '@/components/admin/DataTable';
import StatsCards from '@/components/admin/StatsCards';
interface SystemLog {
    id: number;
    user_id?: number;
    action: string;
    description: string;
    level: string;
    ip_address: string;
    user_agent: string;
    context?: any;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface SystemLogsProps {
    logs: {
        data: SystemLog[];
        current_page: number;
        last_page: number;
        total: number;
    };
}

export default function SystemLogs({ logs }: SystemLogsProps) {
    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '系統日誌', href: '/admin/system-logs' },
    ];

    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [levelFilter, setLevelFilter] = useState('all');

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="系統日誌" description="查看和管理系統操作記錄和錯誤日誌">
            <Head title="系統日誌" />

            <div className="space-y-6 p-6">
                {/* todo 篩選和搜尋區域(元件化) */}
                <SearchAndFilter
                    searchTerm={searchTerm}
                    onSearchChange={setSearchTerm}
                    searchPlaceholder="搜尋日誌..."
                    filters={[
                        {
                            key: 'status',
                            label: '狀態',
                            options: [
                                { value: 'all', label: '所有' },
                                { value: 'success', label: '成功' },
                                { value: 'error', label: '錯誤' },
                            ],
                            value: statusFilter,
                            onChange: setStatusFilter,
                        },
                        {
                            key: 'level',
                            label: '日誌級別',
                            options: [
                                { value: 'all', label: '所有' },
                                { value: 'info', label: '資訊' },
                                { value: 'warning', label: '警告' },
                                { value: 'error', label: '錯誤' },
                            ],
                            value: levelFilter,
                            onChange: setLevelFilter,
                        },
                    ]}
                />

                {/* todo 統計資訊(元件化) */}
                <StatsCards
                    stats={[
                        { title: '總日誌數量', value: logs.total, icon: Info },
                        { title: '當前頁面', value: logs.current_page, icon: Info },
                    ]}
                />

                {/* todo 日誌列表(元件化，使用table呈現) */}
                <DataTable
                    data={logs.data}
                    columns={[
                        { key: 'id', label: 'ID' },
                        { key: 'user.name', label: '用戶' },
                        { key: 'action', label: '操作' },
                        { key: 'description', label: '描述' },
                        { key: 'level', label: '級別', render: (log) => <Badge variant={log.level}>{log.level}</Badge> },
                        { key: 'ip_address', label: 'IP 地址' },
                        { key: 'created_at', label: '時間', render: (log) => new Date(log.created_at).toLocaleString() },
                    ]}
                    title="系統日誌列表"
                    description={`顯示 ${logs.data.length} / ${logs.total} 筆日誌`}
                    emptyMessage="沒有找到符合條件的日誌"
                    actions={[
                        {
                            key: 'view',
                            label: '查看',
                            icon: <Eye className="h-4 w-4" />,
                            variant: 'outline',
                            onClick: (row) => {
                                // 這裡可以添加查看詳情的邏輯
                                console.log('查看日誌:', row.id);
                            },
                        },
                    ]}
                />
            </div>
        </AdminLayout>
    );
}
