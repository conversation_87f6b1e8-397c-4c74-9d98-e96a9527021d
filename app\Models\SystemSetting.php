<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 系統設定模型
 * 
 * 管理系統的各種設定參數，包括時序控制、安全設定、郵件設定等
 */
class SystemSetting extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'description',
        'is_active',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 設定分類常數
     */
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_TIMING = 'timing';
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_EMAIL = 'email';

    /**
     * 資料類型常數
     */
    const TYPE_STRING = 'string';
    const TYPE_JSON = 'json';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_DATETIME = 'datetime';
    const TYPE_INTEGER = 'integer';

    /**
     * 預設系統設定鍵值
     */
    const SYSTEM_OPEN_TIME = 'system.open_time';
    const SYSTEM_CLOSE_TIME = 'system.close_time';
    const RECRUITMENT_PERIOD_START = 'recruitment.period_start';
    const RECRUITMENT_PERIOD_END = 'recruitment.period_end';
    const REMINDER_COOLDOWN_HOURS = 'reminder.cooldown_hours';
    const AUTO_TIMEOUT_DAYS = 'auto.timeout_days';
    const QUESTIONNAIRE_EDIT_LOCKED = 'questionnaire.edit_locked';

    /**
     * 取得設定值
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "system_setting_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)
                ->where('is_active', true)
                ->first();

            if (!$setting) {
                return $default;
            }

            return self::parseValue($setting->value, $setting->type);
        });
    }

    /**
     * 設定值
     */
    public static function set(string $key, $value, string $type = self::TYPE_STRING, string $category = self::CATEGORY_GENERAL, ?string $description = null): bool
    {
        try {
            $formattedValue = self::formatValue($value, $type);

            self::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $formattedValue,
                    'type' => $type,
                    'category' => $category,
                    'description' => $description,
                    'is_active' => true,
                ]
            );

            // 清除快取
            Cache::forget("system_setting_{$key}");

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to set system setting', [
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 解析設定值
     */
    private static function parseValue(string $value, string $type)
    {
        switch ($type) {
            case self::TYPE_JSON:
                return json_decode($value, true);
            case self::TYPE_BOOLEAN:
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case self::TYPE_DATETIME:
                return \Carbon\Carbon::parse($value);
            case self::TYPE_INTEGER:
                return (int) $value;
            default:
                return $value;
        }
    }

    /**
     * 格式化設定值
     */
    private static function formatValue($value, string $type): string
    {
        switch ($type) {
            case self::TYPE_JSON:
                return json_encode($value);
            case self::TYPE_BOOLEAN:
                return $value ? '1' : '0';
            case self::TYPE_DATETIME:
                if ($value instanceof \Carbon\Carbon) {
                    return $value->toDateTimeString();
                }
                return \Carbon\Carbon::parse($value)->toDateTimeString();
            case self::TYPE_INTEGER:
                return (string) (int) $value;
            default:
                return (string) $value;
        }
    }

    /**
     * 檢查系統是否開放
     */
    public static function isSystemOpen(): bool
    {
        $openTime = self::get(self::SYSTEM_OPEN_TIME);
        $closeTime = self::get(self::SYSTEM_CLOSE_TIME);

        if (!$openTime || !$closeTime) {
            return true; // 如果沒有設定時間，預設為開放
        }

        $now = now();
        $open = \Carbon\Carbon::parse($openTime);
        $close = \Carbon\Carbon::parse($closeTime);

        return $now->between($open, $close);
    }

    /**
     * 檢查是否在招生期間
     */
    public static function isRecruitmentPeriod(): bool
    {
        $startTime = self::get(self::RECRUITMENT_PERIOD_START);
        $endTime = self::get(self::RECRUITMENT_PERIOD_END);

        if (!$startTime || !$endTime) {
            return false;
        }

        $now = now();
        $start = \Carbon\Carbon::parse($startTime);
        $end = \Carbon\Carbon::parse($endTime);

        return $now->between($start, $end);
    }

    /**
     * 檢查問卷編輯是否被鎖定
     */
    public static function isQuestionnaireEditLocked(): bool
    {
        return self::get(self::QUESTIONNAIRE_EDIT_LOCKED, false);
    }

    /**
     * 檢查是否在招生期間內
     */
    public static function isInRecruitmentPeriod(): bool
    {
        $now = now();
        $startTime = self::get(self::RECRUITMENT_PERIOD_START);
        $endTime = self::get(self::RECRUITMENT_PERIOD_END);

        if (!$startTime || !$endTime) {
            return false;
        }

        return $now >= $startTime && $now <= $endTime;
    }

    /**
     * 取得招生期間資訊
     */
    public static function getRecruitmentPeriod(): array
    {
        return [
            'start' => self::get(self::RECRUITMENT_PERIOD_START),
            'end' => self::get(self::RECRUITMENT_PERIOD_END),
            'is_active' => self::isInRecruitmentPeriod(),
        ];
    }

    /**
     * 檢查使用者是否可以存取系統
     *
     * @param string $userRole 使用者角色
     * @return array 包含是否可存取和原因的陣列
     */
    public static function canUserAccessSystem(string $userRole): array
    {
        // 管理員總是可以存取
        if ($userRole === 'admin') {
            return [
                'can_access' => true,
                'reason' => null
            ];
        }

        // 檢查系統是否開放
        if (!self::isSystemOpen()) {
            return [
                'can_access' => false,
                'reason' => 'system_closed'
            ];
        }

        // 檢查是否在招生期間
        if (!self::isInRecruitmentPeriod()) {
            return [
                'can_access' => false,
                'reason' => 'not_in_recruitment_period'
            ];
        }

        return [
            'can_access' => true,
            'reason' => null
        ];
    }

    /**
     * 取得提醒冷卻時間（小時）
     */
    public static function getReminderCooldownHours(): int
    {
        return self::get(self::REMINDER_COOLDOWN_HOURS, 24);
    }

    /**
     * 取得自動超時天數
     */
    public static function getAutoTimeoutDays(): int
    {
        return self::get(self::AUTO_TIMEOUT_DAYS, 7);
    }

    /**
     * 按分類取得設定
     */
    public static function getByCategory(string $category)
    {
        return self::where('category', $category)
            ->where('is_active', true)
            ->orderBy('key')
            ->get();
    }

    /**
     * 初始化預設設定
     */
    public static function initializeDefaults(): void
    {
        // 本地系統設定初始化
        $defaults = [
            [
                'key' => 'system.maintenance_mode', // todo 新增維護模式
                'value' => '0',
                'type' => self::TYPE_BOOLEAN,
                'category' => self::CATEGORY_GENERAL,
                'description' => '系統維護模式開關',
            ],
            [
                'key' => self::REMINDER_COOLDOWN_HOURS,
                'value' => '24',
                'type' => self::TYPE_INTEGER,
                'category' => self::CATEGORY_TIMING,
                'description' => '提醒郵件冷卻時間（小時）',
            ],
            [
                'key' => self::AUTO_TIMEOUT_DAYS,
                'value' => '7',
                'type' => self::TYPE_INTEGER,
                'category' => self::CATEGORY_TIMING,
                'description' => '自動超時天數',
            ],
        ];

        // todo 從外部系統抓取同步資料
        try {
            /**
             * 向主系統查詢招生期間設定做為初始化數據
             * 
             * ex: 招生類別/開始時間/結束時間
             * 1. 碩士班甄試招生(測試) 2025/07/01 上午09:00 ~ 2025/08/30 下午05:00
             * 2. 技優甄審指定項目甄審報名(測試) 2025/07/01 上午09:00 ~ 2025/08/30 下午05:00
             * 3. 單獨招收身心障礙學生考試(測試) 2025/07/01 上午09:00 ~ 2025/08/30 下午05:00
             * 
             * 後續提供後台頁面反向管理寫回管理
             */

            $apiUrl = 'http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period';
            $apiSecret = 'OdmzICpznuM7O48V3gJCJaejNWwabcpG'; // 需要替換為實際的 API 密鑰

            try {
                $response = Http::asForm()->post($apiUrl, [
                    'token' => $token,
                    'nuu_api_key' => $apiSecret,
                    'nuu_api_id' => 'get_applicant_data',
                ]);

                // 檢查 HTTP 狀態碼
                if (!$response->successful()) {
                    throw new \Exception('API 請求失敗，狀態碼：' . $response->status());
                }

                // 解析 JSON 回應
                $data = $response->json();

                // 檢查 API 是否成功
                if (!isset($data['status']) || $data['status'] !== 'success') {
                    throw new \Exception('API 回傳非 success：' . json_encode($data));
                }

                // [
                //     {
                //       "exam_id": "2",
                //       "exam_name": "碩士班甄試招生(測試)",
                //       "app_date1_start": "2025/07/01 09:00:00",
                //       "app_date1_end": "2025/08/30 17:00:00"
                //     }
                //     ]
            } catch (\Exception $e) {
            }
        } catch (\Exception $e) {
            Log::error('同步設定 API 失敗', ['error' => $e->getMessage()]);
        }

        foreach ($defaults as $default) {
            self::firstOrCreate(
                ['key' => $default['key']],
                $default
            );
        }
    }
}
