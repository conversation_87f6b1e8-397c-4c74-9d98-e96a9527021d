<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 系統設定模型
 * 
 * 管理系統的各種設定參數，包括時序控制、安全設定、郵件設定等
 */
class SystemSetting extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'description',
        'is_active',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 設定分類常數
     */
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_TIMING = 'timing';
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_EMAIL = 'email';

    /**
     * 資料類型常數
     */
    const TYPE_STRING = 'string';
    const TYPE_JSON = 'json';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_DATETIME = 'datetime';
    const TYPE_INTEGER = 'integer';

    /**
     * 預設系統設定鍵值
     */
    const SYSTEM_OPEN_TIME = 'system.open_time';
    const SYSTEM_CLOSE_TIME = 'system.close_time';
    const RECRUITMENT_PERIOD_START = 'recruitment.period_start';
    const RECRUITMENT_PERIOD_END = 'recruitment.period_end';
    const REMINDER_COOLDOWN_HOURS = 'reminder.cooldown_hours';
    const AUTO_TIMEOUT_DAYS = 'auto.timeout_days';
    const QUESTIONNAIRE_EDIT_LOCKED = 'questionnaire.edit_locked';
    const MAINTENANCE_MODE = 'system.maintenance_mode';

    /**
     * 取得設定值
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = "system_setting_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = self::where('key', $key)
                ->where('is_active', true)
                ->first();

            if (!$setting) {
                return $default;
            }

            return self::parseValue($setting->value, $setting->type);
        });
    }

    /**
     * 設定值
     */
    public static function set(string $key, $value, string $type = self::TYPE_STRING, string $category = self::CATEGORY_GENERAL, ?string $description = null): bool
    {
        try {
            $formattedValue = self::formatValue($value, $type);

            self::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $formattedValue,
                    'type' => $type,
                    'category' => $category,
                    'description' => $description,
                    'is_active' => true,
                ]
            );

            // 清除快取
            Cache::forget("system_setting_{$key}");

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to set system setting', [
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 解析設定值
     */
    private static function parseValue(string $value, string $type)
    {
        switch ($type) {
            case self::TYPE_JSON:
                return json_decode($value, true);
            case self::TYPE_BOOLEAN:
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case self::TYPE_DATETIME:
                return \Carbon\Carbon::parse($value);
            case self::TYPE_INTEGER:
                return (int) $value;
            default:
                return $value;
        }
    }

    /**
     * 格式化設定值
     */
    private static function formatValue($value, string $type): string
    {
        switch ($type) {
            case self::TYPE_JSON:
                return json_encode($value);
            case self::TYPE_BOOLEAN:
                return $value ? '1' : '0';
            case self::TYPE_DATETIME:
                if ($value instanceof \Carbon\Carbon) {
                    return $value->toDateTimeString();
                }
                return \Carbon\Carbon::parse($value)->toDateTimeString();
            case self::TYPE_INTEGER:
                return (string) (int) $value;
            default:
                return (string) $value;
        }
    }

    /**
     * 檢查使用者是否可以存取系統
     *
     * @param string $userRole 使用者角色
     * @return array 包含是否可存取和原因的陣列
     */
    public static function canUserAccessSystem(string $userRole): array
    {
        // 管理員總是可以存取
        if ($userRole === 'admin') {
            return [
                'can_access' => true,
                'reason' => null
            ];
        }

        return [
            'can_access' => true,
            'reason' => null
        ];
    }

    /**
     * 取得提醒冷卻時間（小時）
     */
    public static function getReminderCooldownHours(): int
    {
        return self::get(self::REMINDER_COOLDOWN_HOURS, 24);
    }

    /**
     * 取得自動超時天數
     */
    public static function getAutoTimeoutDays(): int
    {
        return self::get(self::AUTO_TIMEOUT_DAYS, 7);
    }

    /**
     * 檢查系統是否處於維護模式
     */
    public static function isMaintenanceMode(): bool
    {
        return self::get(self::MAINTENANCE_MODE, false);
    }

    /**
     * 設定維護模式
     */
    public static function setMaintenanceMode(bool $enabled): bool
    {
        return self::set(
            self::MAINTENANCE_MODE,
            $enabled,
            self::TYPE_BOOLEAN,
            self::CATEGORY_GENERAL,
            '系統維護模式開關'
        );
    }

    /**
     * 按分類取得設定
     */
    public static function getByCategory(string $category)
    {
        return self::where('category', $category)
            ->where('is_active', true)
            ->orderBy('key')
            ->get();
    }

    /**
     * 檢查特定考試是否在開放期間
     *
     * @param string $examId 考試ID
     * @return bool
     */
    public static function isExamPeriodOpen(string $examId): bool
    {
        $startKey = "recruitment.period_start.{$examId}";
        $endKey = "recruitment.period_end.{$examId}";

        $startTime = self::get($startKey);
        $endTime = self::get($endKey);

        if (!$startTime || !$endTime) {
            // 如果沒有設定特定考試的時間，回退到一般招生期間檢查
            return self::isInRecruitmentPeriod();
        }

        $now = now();
        $start = \Carbon\Carbon::parse($startTime);
        $end = \Carbon\Carbon::parse($endTime);

        return $now->between($start, $end);
    }

    /**
     * 檢查用戶是否在允許的考試期間內
     *
     * @param string|null $examId 考試ID
     * @param string|null $examYear 考試年度
     * @return array 包含是否允許和原因的陣列
     */
    public static function canUserAccessByExamPeriod(?string $examId, ?string $examYear): array
    {
        if (!$examId || !$examYear) {
            return [
                'can_access' => true,
                'reason' => null
            ];
        }

        // 檢查特定考試期間
        if (!self::isExamPeriodOpen($examId)) {
            return [
                'can_access' => false,
                'reason' => 'exam_period_closed',
                'exam_id' => $examId,
                'exam_year' => $examYear
            ];
        }

        return [
            'can_access' => true,
            'reason' => null
        ];
    }

    /**
     * 初始化預設設定
     */
    public static function initializeDefaults(): void
    {
        // 本地系統設定初始化
        $defaults = [
            [
                'key' => self::MAINTENANCE_MODE,
                'value' => '0',
                'type' => self::TYPE_BOOLEAN,
                'category' => self::CATEGORY_GENERAL,
                'description' => '系統維護模式開關',
            ],
            [
                'key' => self::REMINDER_COOLDOWN_HOURS,
                'value' => '24',
                'type' => self::TYPE_INTEGER,
                'category' => self::CATEGORY_TIMING,
                'description' => '提醒郵件冷卻時間（小時）',
            ],
            [
                'key' => self::AUTO_TIMEOUT_DAYS,
                'value' => '7',
                'type' => self::TYPE_INTEGER,
                'category' => self::CATEGORY_TIMING,
                'description' => '自動超時天數',
            ],
        ];

        // 從外部系統同步初始資料
        try {
            $syncService = new \App\Services\ExternalApiSyncService();
            $syncResult = $syncService->syncSystemSettings();

            if ($syncResult['success']) {
                Log::info('系統初始化時成功同步外部資料', $syncResult);
            } else {
                Log::warning('系統初始化時同步外部資料失敗，使用預設值', $syncResult);
            }
        } catch (\Exception $e) {
            Log::error('系統初始化同步失敗', ['error' => $e->getMessage()]);
        }

        foreach ($defaults as $default) {
            self::firstOrCreate(
                ['key' => $default['key']],
                $default
            );
        }
    }
}
