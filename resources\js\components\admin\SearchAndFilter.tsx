import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, Filter, X } from 'lucide-react';

interface FilterOption {
    value: string;
    label: string;
}

interface SearchAndFilterProps {
    searchTerm: string;
    onSearchChange: (value: string) => void;
    searchPlaceholder?: string;
    filters?: {
        key: string;
        label: string;
        value: string;
        options: FilterOption[];
        onChange: (value: string) => void;
    }[];
    onClearFilters?: () => void;
    showClearButton?: boolean;
    className?: string;
    title?: string;
    description?: string;
}

/**
 * 通用搜尋和篩選元件
 * 
 * 提供搜尋框和多個下拉篩選器的組合介面
 */
export default function SearchAndFilter({
    searchTerm,
    onSearchChange,
    searchPlaceholder = '搜尋...',
    filters = [],
    onClearFilters,
    showClearButton = true,
    className = '',
    title = '篩選和搜尋',
    description = '使用下方工具來篩選和搜尋資料'
}: SearchAndFilterProps) {
    
    const hasActiveFilters = searchTerm || filters.some(filter => filter.value !== 'all' && filter.value !== '');

    const handleClearFilters = () => {
        onSearchChange('');
        filters.forEach(filter => filter.onChange('all'));
        onClearFilters?.();
    };

    return (
        <Card className={className}>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            {title}
                        </CardTitle>
                        <CardDescription>{description}</CardDescription>
                    </div>
                    {showClearButton && hasActiveFilters && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleClearFilters}
                            className="flex items-center gap-2"
                        >
                            <X className="h-4 w-4" />
                            清除篩選
                        </Button>
                    )}
                </div>
            </CardHeader>
            <CardContent>
                <div className={`grid gap-4 ${filters.length > 0 ? `grid-cols-1 md:grid-cols-${Math.min(filters.length + 1, 4)}` : 'grid-cols-1'}`}>
                    {/* 搜尋框 */}
                    <div className="relative">
                        <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                        <Input
                            placeholder={searchPlaceholder}
                            value={searchTerm}
                            onChange={(e) => onSearchChange(e.target.value)}
                            className="pl-10"
                        />
                    </div>

                    {/* 篩選器 */}
                    {filters.map((filter) => (
                        <Select 
                            key={filter.key}
                            value={filter.value} 
                            onValueChange={filter.onChange}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder={`選擇${filter.label}`} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">全部{filter.label}</SelectItem>
                                {filter.options.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    ))}
                </div>

                {/* 活躍篩選器顯示 */}
                {hasActiveFilters && (
                    <div className="mt-4 flex flex-wrap gap-2">
                        {searchTerm && (
                            <div className="flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-800">
                                <Search className="h-3 w-3" />
                                搜尋: {searchTerm}
                                <button
                                    onClick={() => onSearchChange('')}
                                    className="ml-1 hover:text-blue-600"
                                >
                                    <X className="h-3 w-3" />
                                </button>
                            </div>
                        )}
                        {filters.map((filter) => {
                            if (filter.value === 'all' || filter.value === '') return null;
                            const selectedOption = filter.options.find(opt => opt.value === filter.value);
                            return (
                                <div
                                    key={filter.key}
                                    className="flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-sm text-green-800"
                                >
                                    {filter.label}: {selectedOption?.label || filter.value}
                                    <button
                                        onClick={() => filter.onChange('all')}
                                        className="ml-1 hover:text-green-600"
                                    >
                                        <X className="h-3 w-3" />
                                    </button>
                                </div>
                            );
                        })}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
