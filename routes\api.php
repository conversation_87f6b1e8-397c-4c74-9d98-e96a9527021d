<?php

use App\Http\Controllers\Auth\ApplicantLoginController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API 路由
|--------------------------------------------------------------------------
|
| 注意：Laravel 會自動為 api.php 添加 /api 前綴
|
| todo 需要新增中間件，僅允許來自API系統的請求
| 因推薦函資料位於此系統，所以須開放給API系統索取資料
*/

/**
 * 報名系統考生登入
 * 
 * 將從報名系統收到的 token 送去 API系統 進行解碼和驗證
 * 取得考生資料後登入後建立或更新考生，並登入系統
 */
Route::get('/auth-from-external', [ApplicantLoginController::class, 'handleExternalAuth'])
    ->middleware(['web'])
    ->name('api.auth.external');
