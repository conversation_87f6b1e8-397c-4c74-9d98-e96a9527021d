<?php

namespace App\Http\Controllers;

use App\Models\Applicant;
use App\Models\QuestionnaireTemplate;
use App\Models\RecommendationLetter;
use App\Models\Recommender;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;

/**
 * 儀表板控制器
 */
class DashboardController extends Controller
{
  // 主要功能：撈資料(依登入人身份索取資料)
  public function index(Request $request)
  {
    $user = Auth::user();
    $role = $user->role;

    $data = [];

    if ($role === 'applicant') {
      // Get applicant_id from session or find by user_id
      $applicantId = Session::get('applicant_id');
      $applicant = null;

      if (!$applicantId) {
        $applicant = Applicant::where('user_id', $user->id)->first();
        $applicantId = $applicant?->id;
      } else {
        $applicant = Applicant::find($applicantId);
      }

      $recommendations = [];
      $applications = [];

      if ($applicantId) {
        $recommendations = RecommendationLetter::where('applicant_id', $applicantId)
          ->with(['applicant.user', 'recommender'])
          ->get([
            'id',
            'applicant_id',
            'application_form_id',
            'department_name',
            'program_type',
            'recommender_department',
            'status',
            'recommender_email',
            'recommender_name',
            'recommender_title',
            'recommender_phone',
            'pdf_path',
            'submitted_at',
            'created_at',
            'updated_at'
          ]);
      }

      if ($applicant && $applicant->external_uid) {
        $applications = Session::get('applications', []); // 從考生登入時的 session 中獲取報名資料

      }

      $data['recommendations'] = $recommendations;
      $data['applications'] = $applications;
      $data['applicant_id'] = $applicantId;
      $data['applicant_info'] = $applicant ? [
        'user' => [
          'id' => $user->id,
          'role' => $user->role,
          'name' => $user->name,
          'email' => $user->email,
          'phone' => $applicant->phone,
        ]
      ] : null;
    }

    if ($role === 'recommender') {
      // Get recommender_id from session or find by user email
      $recommenderId = Session::get('recommender_id');
      $recommender = null;

      if ($recommenderId) {
        $recommender = Recommender::find($recommenderId);
      } else {
        $recommender = Recommender::where('email', $user->email)->first();
      }

      $recommendations = [];
      $questionnaireTemplates = [];

      if ($recommender) {
        $recommendations = RecommendationLetter::where('recommender_email', $recommender->email)
          ->with(['applicant.user'])
          ->get([
            'id',
            'applicant_id',
            'application_form_id',
            'department_name',
            'program_type',
            'status',
            'pdf_path',
            'submitted_at',
            'created_at'
          ]);

        // 預載入問卷模板，按推薦函的系所和學程分組
        $departmentPrograms = $recommendations->map(function ($rec) {
          return [
            'department_name' => $rec->department_name,
            'program_type' => $rec->program_type,
          ];
        })->unique()->values();

        foreach ($departmentPrograms as $dp) {
          $template = QuestionnaireTemplate::where('department_name', $dp['department_name'])
            ->where('program_type', $dp['program_type'])
            ->where('is_active', true)
            ->first();

          if (!$template) {
            // 如果沒有找到特定的模板，使用預設模板
            $template = QuestionnaireTemplate::where('department_name', '通用')
              ->where('program_type', '一般')
              ->where('is_active', true)
              ->first();
          }

          if ($template) {
            // 確保 questions 是陣列格式
            if (is_string($template->questions)) {
              $template->questions = json_decode($template->questions, true);
            }

            $questionnaireTemplates["{$dp['department_name']}_{$dp['program_type']}"] = $template;
          }
        }
      }

      $data['recommendations'] = $recommendations;
      $data['recommender'] = $recommender;
      $data['questionnaire_templates'] = $questionnaireTemplates;
    }

    if ($role === 'admin') {
      // 取得所有推薦函資料
      $recommendations = RecommendationLetter::with(['applicant.user', 'recommender'])
        ->get([
          'id',
          'applicant_id',
          'application_form_id',
          'department_name',
          'program_type',
          'recommender_department',
          'status',
          'recommender_email',
          'recommender_name',
          'submitted_at',
          'created_at',
          'pdf_path'
        ]);

      $data['recommendations'] = $recommendations;
    }

    return Inertia::render('dashboard', [
      'recommendations' => $data['recommendations'] ?? [],
      'applications' => $data['applications'] ?? [],
      'applicant_id' => $data['applicant_id'] ?? null,
      'recommender' => $data['recommender'] ?? null,
      'questionnaire_templates' => $data['questionnaire_templates'] ?? [], // 暫不使用
      'applicant_info' => $data['applicant_info'] ?? null,
      'user_role' => $role,
    ]);
  }
}
